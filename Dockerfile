FROM node:23.11.0-alpine3.21 AS builder

WORKDIR /prj
COPY app /prj/app
COPY ext /prj/ext
COPY resources /prj/resources
COPY src /prj/src
COPY *.json *.js *.css *.html /prj/

RUN npm install
RUN npm run build


FROM caddy:2.10.0-alpine

EXPOSE 8120 8129

WORKDIR /usr/share/caddy/mc2ui
COPY --from=builder /prj/dist /usr/share/caddy/mc2ui
COPY docker/Caddyfile /etc/caddy/Caddyfile
RUN touch /usr/share/caddy/healthz
